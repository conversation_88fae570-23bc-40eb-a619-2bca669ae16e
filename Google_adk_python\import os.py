import os

def write_file(path, content):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, "w") as f:
        f.write(content)

write_file("Google_adk_python/requirements.txt", "streamlit\nsupabase\nadk\nopenai\n")

write_file("plugins/my_custom_tool.py", """from adk.agent import Tool

class MyCustomTool(Tool):
    name = "EchoTool"
    description = "Echoes the user's message."

    def run(self, message: str):
        return f"Echo: {message}"

exported_tool = MyCustomTool()
""")

write_file(".streamlit/secrets.toml", 'OPENAI_API_KEY = "sk-..."')

print("✅ Scaffold complete.")
