import streamlit as st
from adk.agent import Agent, Tool, AgentConfig
from adk.llm import OpenAIChatCompletion
from supabase import create_client, Client
import importlib.util
import os

# 1. Set up ADK agent

class SupabaseQueryTool(Tool):
    name = "SupabaseQueryTool"
    description = "Search/query multiple user-selected Supabase tables."

    def __init__(self, supabase_url, supabase_key, allowed_tables):
        self.client: Client = create_client(supabase_url, supabase_key)
        self.allowed_tables = allowed_tables

    def run(self, query: str, table: str):
        if table not in self.allowed_tables:
            return f"Table {table} is not allowed."
        try:
            data = self.client.table(table).select("*").execute()
            return str(data.data)
        except Exception as e:
            return f"Error: {e}"

# 2. Dynamic tool/plugin loading system

def load_tools(dynamic_tools_path: str):
    tool_instances = []
    if not os.path.isdir(dynamic_tools_path):
        return tool_instances
    for filename in os.listdir(dynamic_tools_path):
        if filename.endswith(".py"):
            filepath = os.path.join(dynamic_tools_path, filename)
            spec = importlib.util.spec_from_file_location("external_tool", filepath)
            if spec is None:
                continue
            module = importlib.util.module_from_spec(spec)
            if spec.loader is None:
                continue
            spec.loader.exec_module(module)
            if hasattr(module, "exported_tool"):
                tool_instances.append(module.exported_tool)
    return tool_instances

# 3. Streamlit UI Setup

st.set_page_config(page_title="Dynamic ADK RAG Chat Agent", layout="wide")
st.title("Dynamic RAG Agent powered by Google's ADK + Supabase")

supabase_url = st.text_input("Supabase URL", type="default")
supabase_key = st.text_input("Supabase Service Key", type="password")
selected_tables = st.text_input("Allowed Tables (comma separated)", "")

uploaded_plugins = st.file_uploader("Add Tool Plugin (.py)", type="py", accept_multiple_files=True)

if 'tools_dir' not in st.session_state:
    st.session_state['tools_dir'] = "plugins"
    os.makedirs(st.session_state['tools_dir'], exist_ok=True)

# Save uploaded plugins
for plugin_file in uploaded_plugins or []:
    save_path = os.path.join(st.session_state['tools_dir'], plugin_file.name)
    with open(save_path, 'wb') as out_file:
        out_file.write(plugin_file.read())

# Load tools
tools = []
if supabase_url and supabase_key and selected_tables:
    table_list = [t.strip() for t in selected_tables.split(",")]
    supabase_tool = SupabaseQueryTool(supabase_url, supabase_key, table_list)
    tools.append(supabase_tool)

dynamic_tools = load_tools(st.session_state['tools_dir'])
tools.extend(dynamic_tools)

# Agent config -- using OpenAI for LLM backend
if st.secrets.get("OPENAI_API_KEY"):
    llm = OpenAIChatCompletion(api_key=st.secrets["OPENAI_API_KEY"])
else:
    llm = OpenAIChatCompletion(api_key=st.text_input("OpenAI API Key", type="password"))

agent_config = AgentConfig(
    tools=tools,
    llm=llm,
    use_tool_routing=True,
    retrieval_enabled=True,
)

agent = Agent(config=agent_config)

# Chat UI
if "chat_log" not in st.session_state:
    st.session_state["chat_log"] = []

user_input = st.text_input("Your message")

if st.button("Send"):
    st.session_state["chat_log"].append(("User", user_input))
    response = agent.chat(user_input)
    st.session_state["chat_log"].append(("Agent", response['text']))

st.markdown("### Chat History")
for role, msg in st.session_state["chat_log"]:
    if role == "User":
        st.markdown(f"<b>User:</b> {msg}", unsafe_allow_html=True)
    else:
        st.markdown(f"<b>Agent:</b> {msg}", unsafe_allow_html=True)
